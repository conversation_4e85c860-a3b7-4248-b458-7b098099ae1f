'use client';

import { useMediaQuery } from '@/lib/hooks/utils/use-media-query';
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useEffect } from 'react';
import { Unity, useUnityContext } from 'react-unity-webgl';
import { useConnect, useAccount } from 'wagmi';

// ✅ Declare the custom global property for TypeScript
declare global {
  interface Window {
    onUnityConnectWalletRequest?: () => void;
    ethereum?: any; // Optional: You can type this more strictly with ethers.js or web3 types
  }
}

export default function UnityWrapper() {
  const isMobile = useMediaQuery('(max-width: 640px)');
  const { connectors, connect } = useConnect();
  const { address, isConnected } = useAccount();

  const { unityProvider, sendMessage } = useUnityContext({
    loaderUrl: '/unity/Build/Build.loader.js',
    dataUrl: '/unity/Build/Build.data',
    frameworkUrl: '/unity/Build/Build.framework.js',
    codeUrl: '/unity/Build/Build.wasm',
  });

  useEffect(() => {
    window.onUnityConnectWalletRequest = async () => {
      try {
        // If already connected, send the current address to Unity
        if (isConnected && address) {
          sendMessage('WalletBridge', 'OnWalletConnected', address);
          return;
        }

        // Find the first available connector (prioritize injected/MetaMask)
        const availableConnector =
          connectors.find(
            (connector) =>
              connector.name.toLowerCase().includes('injected') || connector.name.toLowerCase().includes('metamask'),
          ) || connectors[0]; // Fallback to first connector if no injected found

        if (!availableConnector) {
          alert('No wallet connectors available');
          return;
        }

        // Connect using the selected connector
        connect({ connector: availableConnector });
      } catch (error: any) {
        alert(`Wallet connection failed: ${error.message}`);
      }
    };
  }, [sendMessage, connectors, connect, isConnected, address]);

  // Send address to Unity when connection status changes
  useEffect(() => {
    if (isConnected && address) {
      sendMessage('WalletBridge', 'OnWalletConnected', address);
    }
  }, [isConnected, address, sendMessage]);

  return (
    <div className="flex min-h-screen items-center justify-center  font-inter">
      {/* <Unity unityProvider={unityProvider} style={{ width: 960, height: 600 }} /> */}

      <ConnectButton
        label="Login"
        showBalance={false}
        accountStatus={isMobile ? 'avatar' : 'full'}
        chainStatus={'icon'}
      />
    </div>
  );
}
