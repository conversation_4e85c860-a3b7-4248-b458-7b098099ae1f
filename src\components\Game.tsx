'use client';

import { useMediaQuery } from '@/lib/hooks/utils/use-media-query';
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useEffect } from 'react';
import { Unity, useUnityContext } from 'react-unity-webgl';

// ✅ Declare the custom global property for TypeScript
declare global {
  interface Window {
    onUnityConnectWalletRequest?: () => void;
    ethereum?: any; // Optional: You can type this more strictly with ethers.js or web3 types
  }
}

export default function UnityWrapper() {
  const isMobile = useMediaQuery('(max-width: 640px)');

  const { unityProvider, sendMessage } = useUnityContext({
    loaderUrl: '/unity/Build/Build.loader.js',
    dataUrl: '/unity/Build/Build.data',
    frameworkUrl: '/unity/Build/Build.framework.js',
    codeUrl: '/unity/Build/Build.wasm',
  });

  useEffect(() => {
    window.onUnityConnectWalletRequest = async () => {
      if (typeof window.ethereum === 'undefined') {
        alert('MetaMask not found');
        return;
      }

      try {
        const accounts = await window.ethereum.request({
          method: 'eth_requestAccounts',
        });
        const wallet = accounts[0];

        // Send wallet address back into Unity
        sendMessage('WalletBridge', 'OnWalletConnected', wallet);
      } catch (error: any) {
        alert(`Wallet connection failed: ${error.message}`);
      }
    };
  }, [sendMessage]);

  return (
    <div className="flex min-h-screen items-center justify-center  font-inter">
      {/* <Unity unityProvider={unityProvider} style={{ width: 960, height: 600 }} /> */}

      <ConnectButton
        label="Login"
        showBalance={false}
        accountStatus={isMobile ? 'avatar' : 'full'}
        chainStatus={'icon'}
      />
    </div>
  );
}
